<?php

namespace api\controllers;

use common\helpers\DebugHelper;
use common\libs\Cache;
use common\libs\WxWork;

class SmsController extends BaseApiController
{
    public function actionTxCallback()
    {
        /**
         * 一次回调请求里可能有多次的短信请求结果，以 json 数组的形式。
         * [
         * {
         * "user_receive_time": "2015-10-17 08:03:04",
         * "nationcode": "86",
         * "mobile": "13xxxxxxxxx",
         * "report_status": "SUCCESS",
         * "errmsg": "DELIVRD",
         * "description": "用户短信送达成功",
         * "sid": "xxxxxxx"
         * }
         * ]
         */

        // 暂时关闭短信失败提醒
        if (\Yii::$app->request->isPost) {
            $json = file_get_contents("php://input");
            $data = json_decode($json, true);

            DebugHelper::sms($json);

            // report_status是FAIL的时候，给机器人发一个信息
            // foreach ($data as $item) {
            //     if ($item['report_status'] == 'FAIL') {
            //         $mobile      = $item['mobile'];
            //         $errmsg      = $item['errmsg'];
            //         $description = $item['description'];
            //         $content     = "短信发送失败，手机号：{$mobile}，错误信息：{$errmsg}，描述：{$description}";
            //         WxWork::getInstance()
            //             ->robotMessageToSms($content);
            //     }
            // }
        }
    }

    public function actionAlCallback()
    {
        $get  = \Yii::$app->request->get();
        $post = \Yii::$app->request->post();
        $json = file_get_contents("php://input");
        $data = json_decode($json, true);

        DebugHelper::sms('====阿里云短信回调开始===');
        DebugHelper::sms($get);
        DebugHelper::sms($post);
        /**
         * array (
         * 0 =>
         * array (
         * 'send_time' => '2017-08-30 00:00:00',
         * 'report_time' => '2017-08-30 00:00:00',
         * 'success' => true,
         * 'err_msg' => '用户接收成功',
         * 'err_code' => 'DELIVERED',
         * 'phone_number' => '18612345678',
         * 'sms_size' => '1',
         * 'biz_id' => '932702304080415357^0',
         * 'out_id' => '1184585343',
         * ),
         * )
         */
        DebugHelper::sms($data);
        DebugHelper::sms('====阿里云短信回调结束===');

        // {
        //       "code": 0,
        //       "msg": "成功"
        //     }

        echo json_encode([
            'code' => 0,
            'msg'  => '成功',
        ]);
    }
}