<?php

namespace api\controllers;

use common\base\BaseController;
use Yii;

/**
 * Api应用基类
 * 1、开放规则编写
 *      --IP或域名白名单方式开放
 *      --签名的形式开放
 */
class BaseApiController extends BaseController
{
    public $layout = false;

    /**
     * 发生前在请求前
     * @param \yii\base\Action $action
     * @return bool|void|object
     * @throws \yii\web\BadRequestHttpException
     */
    public function beforeAction($action)
    {
        if (!parent::beforeAction($action)) {
            return false;
        }

        if (in_array($action->uniqueID, $this->ignoreLogin())) {
            return parent::beforeAction($action);
        }

        return $this->unSign();
    }

    /**
     * 发生前在请求前
     * @param \yii\base\Action $action
     * @return bool|void|object
     * @throws \yii\web\BadRequestHttpException
     */
    // public function beforeAction($action)
    // {
    //     //读取所有配置的白名单IP
    //     $configIpWhiteArr = [];//Yii::$app->params['IPWhite'];
    //     $ipWhiteArr       = array_merge($configIpWhiteArr, $this->ipWhiteList);
    //     //获取当前Client IP
    //     $clientIp = $_SERVER['REMOTE_ADDR'];
    //     // 1、如果配置白名单了则不限制请求---一般用在开发或测试
    //     // 2、如果没有则限制IP请求---限制真实用户
    //     // 3、验证签名 签名规则:长度32+必须含有字符串[gaoxiaojob]+2、6、10、16、18、29+redis时效性
    //     if (!in_array($clientIp, $ipWhiteArr)) {//不在白名单-
    //         //获取应用Redis key前缀
    //         $redisPrefix = $this->getRidesPrefixKey();
    //         $redisKey    = $redisPrefix . $clientIp;
    //         //获取当前请求Ip的缓存数据
    //         $ipCacheData = Cache::get($redisKey);
    //         if (!is_null($ipCacheData) && $ipCacheData > $this->ipLimitNum) {
    //             return $this->fail('请求频繁！');
    //         }
    //         //对IP加限制
    //         if (is_null($ipCacheData)) {
    //             Cache::set($redisKey, 1, 60);
    //         } else {
    //             Yii::$app->redis->incr($redisKey);
    //         }
    //     }
    //
    //     return parent::beforeAction($action); // TODO: Change the autogenerated stub
    // }

    /**
     * 发生在请求后
     * @param \yii\base\Action $action
     * @param mixed            $result
     * @return mixed
     */
    public function afterAction($action, $result)
    {
        // 记录日志
        $this->log($action, $result);

        return parent::afterAction($action, $result);
    }

    /**
     * 返回Redis Key前缀
     * @param false $global 是否是全局Key
     * @return mixed|string
     */
    protected function getRidesPrefixKey($global = false)
    {
        //全局前缀
        $globalPrefix = Yii::$app->params['redisPrefix'];
        if (!$global) {
            $platformTag  = Yii::$app->params['platformTag'];
            $globalPrefix = $platformTag . ':' . $globalPrefix . $platformTag . '_';
        } else {
            $globalPrefix = 'All:' . $globalPrefix;
        }

        return $globalPrefix;
    }

    public function ignoreLogin()
    {
        return [
            'test/index',
            'home/index',
            // 'home/set-white-list',
            'announcement/get-list',
            'announcement/get-detail',
            'announcement/get-html',
            'job/get-list',
            'news/get-list',
            'area/get-region-all-select',
            'area/get-job-hot-list',
            'area/get-announcement-hot-list',
            'area/get-company-hot-list',
            //春招活动接口
            'activity-subject-spring/get-recruit-tab-list',
            'activity-subject-spring/get-online-chat-list',
            'activity-subject-spring/get-urgent-recruit-list',
            'activity-subject-spring/get-area-list',
            'activity-subject-spring/get-company-list-by-area-id',
            //综合场活动接口
            'activity-comprehensive/get-recruit-tab-list',
            'activity-comprehensive/get-online-chat-list',
            'activity-comprehensive/get-urgent-recruit-list',
            'activity-comprehensive/get-area-list',
            'activity-comprehensive/get-company-list-by-area-id',
            'sms/tx-callback',
            'sms/al-callback',
            //凌云英才
            'ling-yun-talent/get-company-list',
            'ling-yun-talent/global-recruit',
            'ling-yun-talent/global-recruit-by-skill',
            'ling-yun-talent/global-recruit-by-abroad',
            // 转换接口
            'pdf/convert',
            'monitor/get-network-used',
            'monitor/get-resume-info',
            'automate/upload-file',
            'meilisearch/job-demo'
        ];
    }

    private function log($action, $result)
    {
        // 记录全部的请求日志log
        $log = [
            'url'        => Yii::$app->request->url,
            'method'     => Yii::$app->request->method,
            // 'params'     => json_encode(Yii::$app->request->get()),
            'params'     => Yii::$app->request->get(),
            'ip'         => Yii::$app->request->userIP,
            'user_agent' => Yii::$app->request->userAgent,
            'time'       => CUR_DATETIME,
        ];

        $filePath = Yii::getAlias('@log/api/') . date('/Ymd/');
        if (!is_dir($filePath)) {
            mkdir($filePath, 0777, true);
        }
        // 写文件日志
        $logFile = $filePath . 'api_log.log';

        $logStr = '===============================================================' . PHP_EOL;
        // 时间
        $logStr .= '时间：' . date('Y-m-d H:i:s') . PHP_EOL;
        // 方法

        $logStr .= var_export($log, true) . PHP_EOL;

        // 结果

        file_put_contents($logFile, $logStr, FILE_APPEND);
    }

    protected function sign()
    {
    }

    protected function unSign()
    {
        // 校验token
        $postSign = \Yii::$app->request->post('sign');
        // 时间戳
        $token     = Yii::$app->params['apiToken'];
        $timestamp = CUR_TIMESTAMP;
        // 做一个简单的加密md5
        $sign = md5($timestamp . $token);

        if ($sign != $postSign) {
            // 抛出403
            $this->error('没有权限', 403);

            return false;
        }

        return true;
    }
}