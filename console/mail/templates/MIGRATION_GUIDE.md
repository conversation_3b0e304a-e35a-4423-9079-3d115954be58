# 邮件模板系统迁移指南

## 🎯 迁移目标

将EmailQueue.php中的HTML模板代码拆分到独立的模板文件中，提高代码的可维护性和可读性。

## 📋 迁移进度

### ✅ 已完成的模板

#### 验证类邮件
- [x] 用户注册验证 (`verification/register.php`)
- [x] 修改密码验证 (`verification/change-password.php`)
- [x] 修改邮箱验证 (`verification/change-email.php`)
- [x] 绑定邮箱验证 (`verification/bind-email.php`)

#### 反馈类邮件
- [x] 通过初筛 (`feedback/resume-first-pass.php`)
- [x] 面试邀请 (`feedback/resume-interview.php`)

#### 审核类邮件
- [x] 单位认证通过 (`audit/company-audit-pass.php`)
- [x] 单位认证拒绝 (`audit/company-audit-reject.php`)

#### 投递类邮件
- [x] 求职者投递成功 (`delivery/seeker-delivery.php`)
- [x] 平台投递通知 (`delivery/platform-delivery.php`)

#### 可复用组件
- [x] 用户信息卡片 (`components/user-info-card.php`)
- [x] 推荐区域 (`components/recommend-section.php`)
- [x] 教育经历 (`components/education-section.php`)

### 🔄 待迁移的模板

#### 投递类邮件
- [ ] 站外职位投递 (`delivery/off-site-job-apply.php`)
- [ ] 邮件投递通知 (`delivery/post-delivery.php`)
- [ ] 非合作单位投递 (`delivery/not-will-delivery.php`)

#### 反馈类邮件
- [ ] 简历被查看 (`feedback/resume-check.php`)
- [ ] 简历不合适 (`feedback/resume-back.php`)
- [ ] 职位投递反馈 (`feedback/job-apply.php`)

#### 审核类邮件
- [ ] 职位审核通过 (`audit/job-audit-pass.php`)
- [ ] 职位审核拒绝 (`audit/job-audit-reject.php`)

#### 订阅类邮件
- [ ] 职位订阅推送 (`subscription/job-subscribe.php`)
- [ ] 职位投递邀请 (`subscription/job-invite.php`)
- [ ] 职位更新通知 (`subscription/job-update.php`)

#### 待创建的组件
- [ ] 职位卡片 (`components/job-card.php`)
- [ ] 工作经历 (`components/work-section.php`)
- [ ] 项目经历 (`components/project-section.php`)
- [ ] 学术成果 (`components/achievement-section.php`)
- [ ] 联系信息 (`components/contact-info.php`)

## 🔧 迁移步骤

### 1. 创建新模板文件

```bash
# 创建模板文件
touch console/mail/templates/category/template-name.php
```

### 2. 从EmailQueue.php提取HTML代码

找到对应的case分支，将HTML代码复制到新模板文件中。

### 3. 替换变量绑定

将PHP字符串拼接改为模板变量：

```php
// 原来的代码
$html = '<div>用户名：' . $userName . '</div>';

// 新的模板代码
<div>用户名：<?= $userName ?></div>
```

### 4. 更新EmailTemplateRenderer

在`EmailTemplateRenderer::getTemplateMapping()`中添加映射关系：

```php
EmailQueue::EMAIL_TYPE_XXX => 'category/template-name',
```

### 5. 添加数据准备方法

在EmailQueue.php中添加对应的数据准备方法：

```php
private function prepareXxxData($content, $visitUrl, &$setUrl)
{
    // 准备模板所需的数据
    return $data;
}
```

### 6. 测试验证

运行测试脚本验证模板是否正常工作：

```bash
php console/mail/templates/test-template-system.php
```

## 📝 注意事项

1. **保持向后兼容**: 在迁移过程中，原有的switch逻辑作为后备方案
2. **数据安全**: 确保所有用户数据都经过适当的转义和验证
3. **性能考虑**: 大型模板可以考虑添加缓存机制
4. **代码复用**: 尽量使用组件来减少重复代码

## 🚀 预期收益

- **代码量减少**: EmailQueue.php从1800+行减少到800行左右
- **维护性提升**: HTML模板独立，易于修改和调试
- **开发效率**: 新增邮件类型时只需创建模板文件
- **团队协作**: 前端开发者可以直接修改HTML模板

## 📞 支持

如有问题，请联系开发团队或查看相关文档。
