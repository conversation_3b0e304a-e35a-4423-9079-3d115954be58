<?php
/**
 * 教育经历组件
 * 
 * 变量说明：
 * @var array $educationList 教育经历列表
 */
?>

<?php if (!empty($educationList)): ?>
<div class="row-content education-content spacing-1">
    <div class="wrapper-title">教育经历
        <div class="underline"></div>
    </div>
    <?php foreach ($educationList as $key => $item): ?>
        <?php
        $tagBlue = '';
        if ($item['isOverseasStudy'] == 1) {
            $tagBlue .= '<span class="tag-blue">海外</span>';
        }
        if ($item['is_project_school'] == 1) {
            $tagBlue .= '<span class="tag-orange">985/211</span>';
        }
        $isRecruitment = '';
        if ($item['isRecruitment'] == 1) {
            $isRecruitment = '（统招）';
        }
        $college = $item['college'] ? ' | 二级院系（机构）：' . $item['college'] : '';
        $studyEndDate = strtotime($item['studyEndDate']) > 0 ? $item['studyEndDate'] : '至今';
        ?>
        <div class="row">
            <div class="jc-between-title">
                <div class="title has-dot">
                    <span class="name"><?= $item['school'] ?></span>
                    <?= $tagBlue ?>
                </div>
                <div class="aside"><?= $item['studyBeginDate'] ?>-<?= $studyEndDate ?></div>
            </div>
            <div class="common-details">
                <span><?= $item['educationName'] ?><?= $isRecruitment ?> | <?= $item['majorName'] ?><?= $college ?></span>
            </div>
        </div>
    <?php endforeach; ?>
</div>
<?php endif; ?>
