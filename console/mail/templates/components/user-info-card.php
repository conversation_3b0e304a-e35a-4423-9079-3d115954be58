<?php
/**
 * 用户信息卡片组件
 * 
 * 变量说明：
 * @var array $userInfo 用户信息
 * @var string $userGender 用户性别图标URL
 * @var string $tag 用户标签HTML
 * @var string $memberEmail 用户邮箱
 * @var array $operateLinks 操作链接数组（可选）
 */
?>

<div class="userinfo">
    <div> 
        <img src="<?= $userInfo['avatar'] ?>" class="userphoto"/>
    </div>
    <div class="info">
        <div class="user-basic">
            <span class="name"><?= $userInfo['name'] ?></span>
            <img class="gender" src="<?= $userGender ?>" alt="">
            <div class="tag-content"><?= $tag ?></div>
        </div>
        <div class="education">
            <?= $userInfo['educationName'] ?>丨<?= $userInfo['schoolName'] ?>丨<?= $userInfo['majorTxt'] ?>丨<?= $userInfo['age'] ?>岁
            <?php if (isset($memberEmail)): ?>
                | 联系邮箱：<?= $memberEmail ?>
            <?php endif; ?>
        </div>
        <?php if (isset($operateLinks) && !empty($operateLinks)): ?>
        <div class="operate">
            <?php foreach ($operateLinks as $link): ?>
                <a href="<?= $link['url'] ?>" class="<?= $link['class'] ?>" target="_blank">
                    <span><?= $link['text'] ?></span>
                </a>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
    </div>
</div>
