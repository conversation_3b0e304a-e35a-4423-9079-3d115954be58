<?php
/**
 * 邮件投递通知模板（投递反馈模版B）
 * 
 * 变量说明：
 * @var array $content 邮件内容数据
 * @var array $userInfo 用户信息
 * @var array $jobModel 职位信息
 * @var string $userGender 用户性别图标
 * @var string $tag 用户标签
 * @var string $memberEmail 用户邮箱
 * @var string $visitUrl 网站访问地址
 * @var string $department 部门信息
 * @var array $recommendList 推荐列表
 * @var string $backLink 职位链接
 * @var string $contactLink 联系链接
 * @var string $operateLink1 通过初筛链接
 * @var string $operateLink2 暂不合适链接
 * @var string $advantage 个人优势HTML
 * @var array $info 详细简历信息
 */
?>

<div class="email-body">
    <div class="hi">Hi，尊敬的招聘方：</div>
    <div class="email-content">
        <b><?= $userInfo["name"] ?></b>投递了您的职位<a href="<?= $backLink ?>" target="_blank" class="primary"><?= $jobModel['name'] ?><?= $department ?></a>，快去看看吧~
    </div>
</div>

<div class="row-content">
    <div class="date">
        <span class="recently">最近登录时间：<?= $userInfo['last_login_time'] ?></span>
        <a href="<?= $visitUrl ?>/member/company/resume/apply/list" target="_blank" class="login">登录高校人才网查看更多简历</a>
    </div>
    
    <div class="info-content clear">
        <div class="avatar" style="background-image: url(<?= $userInfo["avatar"] ?>);"></div>
        <div class="info">
            <div class="row user-basic">
                <span class="name"><?= $userInfo["name"] ?></span>
                <img class="gender" src="<?= $userGender ?>" width="20px" alt=""/>
                <div class="tag-content"><?= $tag ?></div>
            </div>
            <div class="row clear">
                <?= $userInfo["educationName"] ?> | <?= $userInfo["schoolName"] ?> | <?= $userInfo["majorTxt"] ?> | <?= $userInfo["age"] ?>岁 | 联系邮箱：<?= $memberEmail ?>
            </div>
            <div class="row operate">
                <a href="<?= $contactLink ?>" target="_blank" class="chat"><span>在线沟通</span></a>
                <a href="<?= $contactLink ?>" target="_blank" class="primary">联系人才</a>
                <a href="<?= $operateLink1 ?>" target="_blank">通过初筛</a>
                <a href="<?= $operateLink2 ?>" target="_blank">暂不合适</a>
            </div>
        </div>
    </div>
    <?= $advantage ?>
</div>

<?php
// 渲染求职意向
if (!empty($info['intentionList'])):
    $triangle = 'https://img.gaoxiaojob.com/uploads/static/image/resume/title.png';
?>
<div class="row-content intention-content spacing-1">
    <div class="wrapper-title">求职意向
        <div class="underline"></div>
    </div>
    <div class="row status">
        <img src="<?= $triangle ?>" width="16px" alt="">
        求职状态：<?= $userInfo['workStatusName'] ?>；到岗时间：<?= $userInfo['arriveDateTypeName'] ?>
    </div>
    <?php foreach ($info['intentionList'] as $value): ?>
    <div class="row has-dot">
        <span class="name"><?= $value['jobCategoryName'] ?></span>
        <span>&nbsp;<?= $value['areaName'] ?>&nbsp;|</span>
        <span>&nbsp;<?= $value['wageName'] ?>&nbsp;|</span>
        <span>&nbsp;<?= $value['natureName'] ?></span>
    </div>
    <?php endforeach; ?>
</div>
<?php endif; ?>

<?php
// 渲染教育经历组件
if (!empty($info['educationList'])) {
    echo $this->render('@console/mail/templates/components/education-section', [
        'educationList' => $info['educationList']
    ]);
}
?>

<?php
// 渲染推荐区域组件
if (!empty($recommendList)) {
    echo $this->render('@console/mail/templates/components/recommend-section', [
        'recommendList' => $recommendList,
        'moreLink' => $visitUrl . '/member/company/talent/search',
        'visitUrl' => $visitUrl
    ]);
}
?>
