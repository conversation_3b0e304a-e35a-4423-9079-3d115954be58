<?php
/**
 * 非合作单位投递邮件模板
 * 
 * 变量说明：
 * @var array $content 邮件内容数据
 * @var array $userInfo 用户信息
 * @var array $jobModel 职位信息
 * @var string $userGender 用户性别图标
 * @var string $tag 用户标签
 * @var string $memberEmail 用户邮箱
 * @var string $memberMobile 用户手机
 * @var string $visitUrl 网站访问地址
 * @var string $department 部门信息
 * @var array $recommendList 推荐列表
 * @var string $backLink 职位链接
 * @var string $detailLink 详情链接
 */
?>

<div class="email-body">
    <div>Hi，尊敬的招聘方：</div>
    <div class="email-content">
        <span class="name"><?= $userInfo["name"] ?></span>
        投递了您的职位<a target="_blank" href="<?= $backLink ?>" class="position"><?= $jobModel['name'] ?><?= $department ?></a>，快去看看吧~
    </div>
</div>

<div class="date">
    <span class="recently">最近登录时间：<?= $userInfo['last_login_time'] ?></span>
    <a class="login" href="<?= $detailLink ?>">登录高校人才网查看更多简历</a>
</div>

<div class="userinfo">
    <div> 
        <img src="<?= $userInfo["avatar"] ?>" class="userphoto"/>
    </div>
    <div class="info">
        <div class="user-basic">
            <b class="name"><?= $userInfo["name"] ?></b>
            <img class="gender" src="<?= $userGender ?>" alt="">
            <div class="tag-content"><?= $tag ?></div>
        </div>
        <div class="education">
            <?= $userInfo["educationName"] ?>丨<?= $userInfo["schoolName"] ?>丨<?= $userInfo["majorTxt"] ?>丨<?= $userInfo["age"] ?>岁
        </div>
        <div class="contact">联系电话：<?= $memberMobile ?>丨 联系邮箱：<span class="email-address"><?= $memberEmail ?></span></div>
        <div class="operate">
            <a href="<?= $detailLink ?>" class="chat" target="_blank"><span>在线沟通</span></a>
            <a href="<?= $detailLink ?>" class="more" target="_blank">查看更多简历信息</a>
        </div>
    </div>
</div>

<?php
// 渲染推荐区域组件
if (!empty($recommendList)) {
    echo $this->render('@console/mail/templates/components/recommend-section', [
        'recommendList' => $recommendList,
        'moreLink' => $detailLink,
        'visitUrl' => $visitUrl
    ]);
}
?>
