<?php
/**
 * 平台投递邮件模板（合作单位）
 * 
 * 变量说明：
 * @var array $content 邮件内容数据
 * @var array $userInfo 用户信息
 * @var array $jobModel 职位信息
 * @var string $userGender 用户性别图标
 * @var string $tag 用户标签
 * @var string $memberEmail 用户邮箱
 * @var string $visitUrl 网站访问地址
 * @var string $department 部门信息
 * @var array $recommendList 推荐列表
 * @var string $positionLink 职位链接
 * @var string $detailLink 详情链接
 * @var string $loginLink 登录链接
 * @var string $contactLink 联系链接
 */
?>

<div class="email-body">
    <div class="hi">Hi，尊敬的招聘方：</div>
    <div class="email-content">
        <span class="name"><?= $userInfo["name"] ?></span>
        投递了您的职位<a target="_blank" href="<?= $positionLink ?>" class="position"><?= $jobModel['name'] ?><?= $department ?></a>，快去看看吧~
    </div>
</div>

<div class="date">
    <span class="recently">最近登录时间：<?= $userInfo['last_login_time'] ?></span>
    <a href="<?= $loginLink ?>" target="_blank" class="login">登录高校人才网查看更多简历</a>
</div>

<?php
// 渲染用户信息卡片组件
$operateLinks = [
    ['url' => $contactLink, 'class' => 'chat', 'text' => '在线沟通'],
    ['url' => $detailLink, 'class' => 'more', 'text' => '查看更多简历信息']
];

echo $this->render('@console/mail/templates/components/user-info-card', [
    'userInfo' => $userInfo,
    'userGender' => $userGender,
    'tag' => $tag,
    'memberEmail' => $memberEmail,
    'operateLinks' => $operateLinks
]);
?>

<?php
// 渲染推荐区域组件
if (!empty($recommendList)) {
    echo $this->render('@console/mail/templates/components/recommend-section', [
        'recommendList' => $recommendList,
        'moreLink' => $visitUrl . '/member/company/talent/search',
        'visitUrl' => $visitUrl
    ]);
}
?>
