<?php
/**
 * 职位订阅推送邮件模板
 * 
 * 变量说明：
 * @var array $content 邮件内容数据
 * @var string $resumeName 求职者姓名
 * @var string $resumeLink 简历链接
 * @var array $jobList 职位列表
 * @var string $unCompleteText 未完成模块提示
 * @var string $visitUrl 网站访问地址
 */
?>

<div class="container">
    <div class="email-body">
        <div class="hi"><?= $resumeName ?>，您好：</div>
        <div class="email-content">高校人才网根据您的职位订阅条件，为您精挑细选一批优质职位，推荐您投递！这些岗位目前正在急聘中，现在正是投递好时机~</div>
        <?php if ($unCompleteText): ?>
        <div class="resume-tips">
            <span class="warning">*</span>
            温馨提示：您的简历中
            <span class="wait-perfect"><?= $unCompleteText ?></span>
            未填写完整，详尽的简历内容更容易提高竞争力、受到用人部门青睐哦。
            <a href="<?= $resumeLink ?>" target="_blank">去完善简历</a>
        </div>
        <?php endif; ?>
    </div>
    
    <?php if (!empty($jobList)): ?>
    <div class="recommend-wrapper">
        <div class="content">
            <div class="recommend-title">
                <b class="big">我的订阅</b>
                <span class="recommend-tips">高校人才网根据您的订阅条件，为您精选以下职位</span>
            </div>
            <div class="recommend-content">
                <?php foreach ($jobList as $item): ?>
                <a target="_blank" href="<?= $item['url'] ?>" class="recommend-card">
                    <div class="job-top">
                        <div class="job-name"><?= $item['jobName'] ?></div>
                        <div class="release-date"><?= $item['refreshDate'] ?>发布</div>
                    </div>
                    <div class="tag-content">
                        <span class="tag"><?= $item['education'] ?></span>
                        <?php if ($item['majorName']): ?>
                        <span class="tag"><?= $item['majorName'] ?></span>
                        <?php endif; ?>
                        <span class="tag"><?= $item['amount'] ?>人</span>
                    </div>
                    <div class="job-bottom">
                        <div class="organizational"><?= $item['companyName'] ?></div>
                        <div class="address" style="display:<?= $item['cityShow'] ?>">
                            <img src="<?= $item['address'] ?>"/><?= $item['city'] ?>
                        </div>
                    </div>
                </a>
                <?php endforeach; ?>
            </div>
            <div class="more">
                <a target="_blank" href="<?= $visitUrl ?>/job">查看更多精选职位</a>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>
