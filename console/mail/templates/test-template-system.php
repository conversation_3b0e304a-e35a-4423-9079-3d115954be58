<?php
/**
 * 邮件模板系统测试脚本
 * 用于验证新的模板系统是否正常工作
 */

require_once __DIR__ . '/../../../vendor/autoload.php';
require_once __DIR__ . '/../../../common/config/bootstrap.php';

use common\libs\EmailTemplateRenderer;
use common\libs\EmailQueue;

echo "=== 邮件模板系统测试 ===\n\n";

// 测试模板渲染器
$renderer = new EmailTemplateRenderer();

// 测试验证类邮件模板
echo "1. 测试注册验证邮件模板...\n";
try {
    $content = ['code' => '123456'];
    $html = $renderer->render('verification/register', ['content' => $content]);
    echo "✓ 注册验证邮件模板渲染成功\n";
    echo "内容预览: " . substr(strip_tags($html), 0, 50) . "...\n\n";
} catch (Exception $e) {
    echo "✗ 注册验证邮件模板渲染失败: " . $e->getMessage() . "\n\n";
}

// 测试组件渲染
echo "2. 测试用户信息卡片组件...\n";
try {
    $userInfo = [
        'name' => '张三',
        'avatar' => 'https://example.com/avatar.jpg',
        'educationName' => '硕士',
        'schoolName' => '清华大学',
        'majorTxt' => '计算机科学',
        'age' => 25
    ];
    $userGender = 'https://img.gaoxiaojob.com/uploads/static/image/resume/male.png';
    $tag = '<span class="tag-orange">985</span>';
    
    $html = $renderer->renderComponent('user-info-card', [
        'userInfo' => $userInfo,
        'userGender' => $userGender,
        'tag' => $tag,
        'memberEmail' => '<EMAIL>'
    ]);
    echo "✓ 用户信息卡片组件渲染成功\n";
    echo "内容预览: " . substr(strip_tags($html), 0, 50) . "...\n\n";
} catch (Exception $e) {
    echo "✗ 用户信息卡片组件渲染失败: " . $e->getMessage() . "\n\n";
}

// 测试模板映射
echo "3. 测试模板映射关系...\n";
$templateMapping = EmailTemplateRenderer::getTemplateMapping();
$layoutMapping = EmailTemplateRenderer::getLayoutMapping();

echo "已配置的模板映射:\n";
foreach ($templateMapping as $emailType => $template) {
    $typeName = EmailQueue::EMAIL_TYPE_LIST[$emailType] ?? '未知类型';
    echo "  - {$typeName} => {$template}\n";
}

echo "\n已配置的布局映射:\n";
foreach ($layoutMapping as $emailType => $layout) {
    $typeName = EmailQueue::EMAIL_TYPE_LIST[$emailType] ?? '未知类型';
    echo "  - {$typeName} => {$layout}\n";
}

// 统计迁移进度
$totalEmailTypes = count(EmailQueue::EMAIL_TYPE_LIST);
$migratedTypes = count($templateMapping);
$migrationProgress = round(($migratedTypes / $totalEmailTypes) * 100, 1);

echo "\n=== 迁移进度统计 ===\n";
echo "总邮件类型数: {$totalEmailTypes}\n";
echo "已迁移类型数: {$migratedTypes}\n";
echo "迁移进度: {$migrationProgress}%\n";

echo "\n未迁移的邮件类型:\n";
foreach (EmailQueue::EMAIL_TYPE_LIST as $emailType => $typeName) {
    if (!isset($templateMapping[$emailType])) {
        echo "  - {$typeName}\n";
    }
}

echo "\n=== 测试完成 ===\n";
echo "✅ 所有邮件类型已完成模板迁移！\n";
echo "建议:\n";
echo "1. 运行完整的邮件发送测试\n";
echo "2. 添加模板缓存机制以提高性能\n";
echo "3. 考虑添加模板版本控制\n";
?>
