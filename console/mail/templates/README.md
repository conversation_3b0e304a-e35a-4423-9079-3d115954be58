# 邮件模板目录说明

## 目录结构

```
templates/
├── verification/          # 验证类邮件模板
│   ├── register.php      # 用户注册验证
│   ├── change-password.php # 修改密码验证
│   ├── change-email.php  # 修改邮箱验证
│   └── bind-email.php    # 绑定邮箱验证
├── delivery/             # 投递类邮件模板
│   ├── platform-delivery.php    # 平台投递通知
│   ├── post-delivery.php        # 邮件投递通知
│   ├── not-will-delivery.php    # 非合作单位投递
│   ├── seeker-delivery.php      # 求职者投递成功
│   └── off-site-job-apply.php   # 站外职位投递
├── feedback/             # 反馈类邮件模板
│   ├── resume-check.php         # 简历被查看
│   ├── resume-first-pass.php    # 通过初筛
│   ├── resume-interview.php     # 面试邀请
│   ├── resume-back.php          # 简历不合适
│   └── job-apply.php            # 职位投递反馈
├── audit/                # 审核类邮件模板
│   ├── job-audit-pass.php       # 职位审核通过
│   ├── job-audit-reject.php     # 职位审核拒绝
│   ├── company-audit-pass.php   # 单位认证通过
│   └── company-audit-reject.php # 单位认证拒绝
├── subscription/         # 订阅类邮件模板
│   ├── job-subscribe.php        # 职位订阅推送
│   ├── job-invite.php           # 职位投递邀请
│   └── job-update.php           # 职位更新通知
└── components/           # 可复用组件
    ├── user-info-card.php       # 用户信息卡片
    ├── job-card.php             # 职位卡片
    ├── recommend-section.php    # 推荐区域
    ├── education-section.php    # 教育经历
    ├── work-section.php         # 工作经历
    ├── project-section.php      # 项目经历
    ├── achievement-section.php  # 学术成果
    └── contact-info.php         # 联系信息
```

## 使用说明

1. 每个模板文件都是独立的HTML片段
2. 使用PHP变量进行数据绑定
3. 可以通过include引用components中的组件
4. 所有模板都会被包装在基础布局中

## 变量约定

- `$content`: 邮件内容数据（JSON解码后的数组）
- `$userInfo`: 用户信息
- `$jobInfo`: 职位信息
- `$companyInfo`: 公司信息
- `$visitUrl`: 网站访问地址
- `$baseUrl`: 基础URL
