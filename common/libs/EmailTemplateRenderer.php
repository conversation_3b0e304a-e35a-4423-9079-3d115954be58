<?php

namespace common\libs;

use Yii;
use yii\base\Exception;

/**
 * 邮件模板渲染器
 * 用于渲染邮件模板和组件
 */
class EmailTemplateRenderer
{
    private $templateBasePath;
    private $componentBasePath;
    
    public function __construct()
    {
        $this->templateBasePath = '@console/mail/templates';
        $this->componentBasePath = '@console/mail/templates/components';
    }
    
    /**
     * 渲染邮件模板
     * @param string $template 模板路径（相对于templates目录）
     * @param array $data 模板数据
     * @return string 渲染后的HTML
     * @throws Exception
     */
    public function render($template, $data = [])
    {
        $templateFile = $this->templateBasePath . '/' . $template . '.php';
        
        if (!file_exists(Yii::getAlias($templateFile))) {
            throw new Exception("邮件模板文件不存在: {$templateFile}");
        }
        
        return Yii::$app->view->renderFile($templateFile, $data);
    }
    
    /**
     * 渲染组件
     * @param string $component 组件名称
     * @param array $data 组件数据
     * @return string 渲染后的HTML
     * @throws Exception
     */
    public function renderComponent($component, $data = [])
    {
        $componentFile = $this->componentBasePath . '/' . $component . '.php';
        
        if (!file_exists(Yii::getAlias($componentFile))) {
            throw new Exception("邮件组件文件不存在: {$componentFile}");
        }
        
        return Yii::$app->view->renderFile($componentFile, $data);
    }
    
    /**
     * 渲染带布局的完整邮件
     * @param string $template 模板路径
     * @param array $data 模板数据
     * @param string $layout 布局文件（默认为common）
     * @return string 完整的邮件HTML
     * @throws Exception
     */
    public function renderWithLayout($template, $data = [], $layout = 'common')
    {
        // 渲染模板内容
        $html = $this->render($template, $data);
        
        // 准备布局数据
        $layoutData = array_merge($data, [
            'html' => $html,
        ]);
        
        // 渲染布局
        $layoutFile = '@console/mail/' . $layout . '.php';
        return Yii::$app->view->renderFile($layoutFile, $layoutData);
    }
    
    /**
     * 获取模板映射关系
     * @return array
     */
    public static function getTemplateMapping()
    {
        return [
            EmailQueue::EMAIL_TYPE_REGISTER => 'verification/register',
            EmailQueue::EMAIL_TYPE_CHANGE_PASSWORD => 'verification/change-password',
            EmailQueue::EMAIL_TYPE_CHANGE_EMAIL => 'verification/change-email',
            EmailQueue::EMAIL_TYPE_BIND_EMAIL => 'verification/bind-email',
            EmailQueue::EMAIL_TYPE_OFF_SITE_JOB_APPLY => 'delivery/off-site-job-apply',
            EmailQueue::EMAIL_TYPE_JOB_AUDIT_REJECT => 'audit/job-audit-reject',
            EmailQueue::EMAIL_TYPE_JOB_AUDIT_PASS => 'audit/job-audit-pass',
            EmailQueue::EMAIL_TYPE_RESUME_CHECK => 'feedback/resume-check',
            EmailQueue::EMAIL_TYPE_RESUME_FIRST_PASS => 'feedback/resume-first-pass',
            EmailQueue::EMAIL_TYPE_RESUME_INTERVIEW => 'feedback/resume-interview',
            EmailQueue::EMAIL_TYPE_RESUME_BACK => 'feedback/resume-back',
            EmailQueue::EMAIL_TYPE_JOB_UPDATE => 'subscription/job-update',
            EmailQueue::EMAIL_TYPE_JOB_APPLY => 'feedback/job-apply',
            EmailQueue::EMAIL_COMPANY_INFO_AUDIT_PASS => 'audit/company-audit-pass',
            EmailQueue::EMAIL_COMPANY_INFO_AUDIT_REJECT => 'audit/company-audit-reject',
            EmailQueue::EMAIL_RESUME_INVITE_JOB_APPLY => 'subscription/job-invite',
            EmailQueue::EMAIL_PLATFORM_DELIVERY => 'delivery/platform-delivery',
            EmailQueue::EMAIL_POST_DELIVERY => 'delivery/post-delivery',
            EmailQueue::EMAIL_NOT_WILL_DELIVERY => 'delivery/not-will-delivery',
            EmailQueue::EMAIL_SEEKER_DELIVERY => 'delivery/seeker-delivery',
            EmailQueue::EMAIL_JOB_SUBSCRIBE => 'subscription/job-subscribe',
        ];
    }
    
    /**
     * 获取布局映射关系
     * @return array
     */
    public static function getLayoutMapping()
    {
        return [
            EmailQueue::EMAIL_TYPE_RESUME_INTERVIEW => 'interview',
            EmailQueue::EMAIL_TYPE_RESUME_BACK => 'deliverFeedback',
            EmailQueue::EMAIL_RESUME_INVITE_JOB_APPLY => 'jobInvite',
            EmailQueue::EMAIL_PLATFORM_DELIVERY => 'deliver_notice_a',
            EmailQueue::EMAIL_POST_DELIVERY => 'deliver_notice_b',
            EmailQueue::EMAIL_NOT_WILL_DELIVERY => 'deliver_notice_a',
            EmailQueue::EMAIL_SEEKER_DELIVERY => 'deliverSucceeded',
            EmailQueue::EMAIL_JOB_SUBSCRIBE => 'deliverFeedback',
            // 默认使用common布局
        ];
    }
}
